import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
   Card,
   CardContent,
   CardDescription,
   CardHeader,
   CardTitle,
} from "@/components/ui/card";
import { Award, Camera, CheckCircle, Heart, Star, Users } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

const values = [
   {
      icon: Heart,
      title: "Passion",
      description:
         "We are passionate about capturing the beauty and emotion in every moment, bringing your stories to life through our lens.",
   },
   {
      icon: Award,
      title: "Excellence",
      description:
         "We strive for excellence in every shot, ensuring the highest quality in both our photography and customer service.",
   },
   {
      icon: Users,
      title: "Connection",
      description:
         "We believe in building genuine connections with our clients to understand their vision and exceed their expectations.",
   },
   {
      icon: Camera,
      title: "Creativity",
      description:
         "Our creative approach ensures that every photo and video is unique, artistic, and tells your story in the most beautiful way.",
   },
];

const achievements = [
   "8+ Years of Professional Experience",
   "500+ Happy Clients",
   "50,000+ Photos Captured",
   "300+ Events Covered",
   "Featured in Local Wedding Magazines",
   "5-Star Average Client Rating",
];

const services = [
   "Wedding Photography & Videography",
   "Pre-Wedding & Engagement Shoots",
   "Pregnancy & Maternity Photography",
   "Child Dedication Ceremonies",
   "360 Video Booth Rental",
   "Dry Ice Machine Rental",
   "Corporate Events",
   "Family Portraits",
];

export default function AboutPage() {
   return (
      <div className="min-h-screen">
         {/* Hero Section */}
         <section className="relative py-20 lg:py-32">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
               <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                  <div>
                     <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
                        About{" "}
                        <span className="text-primary">Astral Studios</span>
                     </h1>
                     <p className="text-lg text-muted-foreground mb-8 leading-relaxed">
                        We are a passionate team of photographers and
                        videographers dedicated to capturing life's most
                        precious moments. With over 8 years of experience, we
                        specialize in creating timeless memories that you'll
                        treasure forever.
                     </p>
                     <div className="flex flex-col sm:flex-row gap-4">
                        <Button asChild size="lg">
                           <Link href="/portfolio">View Our Work</Link>
                        </Button>
                        <Button asChild variant="outline" size="lg">
                           <Link href="/contact">Get in Touch</Link>
                        </Button>
                     </div>
                  </div>
                  <div className="relative">
                     <div className="relative aspect-[4/3] rounded-lg overflow-hidden">
                        <Image
                           src="/images/lead-1.JPG"
                           alt="Astral Studios team at work"
                           fill
                           className="object-cover"
                           sizes="(max-width: 768px) 100vw, 50vw"
                        />
                     </div>
                     <div className="absolute -bottom-6 -right-6 bg-primary text-primary-foreground p-6 rounded-lg">
                        <div className="text-2xl font-bold">8+</div>
                        <div className="text-sm">Years Experience</div>
                     </div>
                  </div>
               </div>
            </div>
         </section>

         {/* Our Story */}
         <section className="py-20 bg-muted/50">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
               <div className="max-w-4xl mx-auto text-center">
                  <h2 className="text-3xl md:text-4xl font-bold mb-8">
                     Our Story
                  </h2>
                  <div className="prose prose-lg mx-auto text-muted-foreground">
                     <p className="mb-6">
                        Astral Studios was born from a simple belief: every
                        moment has a story worth telling. Founded with a passion
                        for capturing the beauty in life's most significant
                        events, we have grown from a small local studio to a
                        trusted name in professional photography and videography
                        across the UK.
                     </p>
                     <p className="mb-6">
                        Our journey began with wedding photography, where we
                        discovered our love for documenting love stories. Over
                        the years, we've expanded our services to include
                        pre-wedding shoots, pregnancy photography, child
                        dedications, and event coverage. Each project is
                        approached with the same dedication to excellence and
                        artistic vision.
                     </p>
                     <p>
                        What sets us apart is our commitment to understanding
                        each client's unique vision. We don't just take photos;
                        we create visual narratives that capture the essence of
                        your special moments, ensuring they can be relived and
                        cherished for generations to come.
                     </p>
                  </div>
               </div>
            </div>
         </section>

         {/* Our Values */}
         <section className="py-20">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
               <div className="text-center mb-16">
                  <h2 className="text-3xl md:text-4xl font-bold mb-4">
                     Our Values
                  </h2>
                  <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                     These core values guide everything we do and shape the way
                     we work with our clients.
                  </p>
               </div>

               <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                  {values.map((value, index) => (
                     <Card key={index} className="text-center">
                        <CardHeader>
                           <div className="flex justify-center mb-4">
                              <value.icon className="h-12 w-12 text-primary" />
                           </div>
                           <CardTitle>{value.title}</CardTitle>
                        </CardHeader>
                        <CardContent>
                           <CardDescription className="text-center">
                              {value.description}
                           </CardDescription>
                        </CardContent>
                     </Card>
                  ))}
               </div>
            </div>
         </section>

         {/* Achievements & Services */}
         <section className="py-20 bg-muted/50">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
               <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                  {/* Achievements */}
                  <div>
                     <h3 className="text-2xl font-bold mb-8">
                        Our Achievements
                     </h3>
                     <div className="space-y-4">
                        {achievements.map((achievement, index) => (
                           <div
                              key={index}
                              className="flex items-center space-x-3"
                           >
                              <CheckCircle className="h-5 w-5 text-primary flex-shrink-0" />
                              <span className="text-muted-foreground">
                                 {achievement}
                              </span>
                           </div>
                        ))}
                     </div>
                  </div>

                  {/* Services */}
                  <div>
                     <h3 className="text-2xl font-bold mb-8">What We Offer</h3>
                     <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                        {services.map((service, index) => (
                           <Badge
                              key={index}
                              variant="secondary"
                              className="justify-start p-3"
                           >
                              {service}
                           </Badge>
                        ))}
                     </div>
                  </div>
               </div>
            </div>
         </section>

         {/* Mission & Vision */}
         <section className="py-20">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
               <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                  <Card className="p-8">
                     <CardHeader className="p-0 mb-6">
                        <CardTitle className="text-2xl flex items-center">
                           <Star className="h-6 w-6 text-primary mr-3" />
                           Our Mission
                        </CardTitle>
                     </CardHeader>
                     <CardContent className="p-0">
                        <p className="text-muted-foreground leading-relaxed">
                           To capture and preserve life's most precious moments
                           through exceptional photography and videography,
                           creating timeless memories that bring joy and
                           connection to families and couples for generations to
                           come. We are committed to providing personalized
                           service that exceeds expectations while maintaining
                           the highest standards of artistic excellence.
                        </p>
                     </CardContent>
                  </Card>

                  <Card className="p-8">
                     <CardHeader className="p-0 mb-6">
                        <CardTitle className="text-2xl flex items-center">
                           <Camera className="h-6 w-6 text-primary mr-3" />
                           Our Vision
                        </CardTitle>
                     </CardHeader>
                     <CardContent className="p-0">
                        <p className="text-muted-foreground leading-relaxed">
                           To be the most trusted and sought-after photography
                           and videography studio in the UK, known for our
                           creative vision, technical expertise, and genuine
                           care for our clients. We envision a future where
                           every family has access to professional photography
                           services that help them celebrate and remember their
                           most important milestones.
                        </p>
                     </CardContent>
                  </Card>
               </div>
            </div>
         </section>

         {/* CTA Section */}
         <section className="py-20 bg-primary text-primary-foreground">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
               <h2 className="text-3xl md:text-4xl font-bold mb-4">
                  Ready to Work Together?
               </h2>
               <p className="text-lg mb-8 max-w-2xl mx-auto opacity-90">
                  Let's discuss how we can capture your special moments and
                  create beautiful memories that will last a lifetime.
               </p>
               <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" variant="secondary">
                     <Link href="/contact">Get Your Quote</Link>
                  </Button>
                  <Button
                     asChild
                     size="lg"
                     variant="outline"
                     className="border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary"
                  >
                     <Link href="/services">View Our Services</Link>
                  </Button>
               </div>
            </div>
         </section>
      </div>
   );
}
