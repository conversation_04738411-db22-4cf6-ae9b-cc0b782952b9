import { HeroSection } from "@/components/hero-section";
import { ServiceCard } from "@/components/service-card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
   Card,
   CardContent,
   CardDescription,
   CardHeader,
   CardTitle,
} from "@/components/ui/card";
import { Award, Camera, Quote, Star, Users, Video } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

const featuredServices = [
   {
      title: "Wedding Photography",
      description:
         "Capture your special day with stunning, timeless photographs that tell your unique love story.",
      image: "/images/wedding-shoots/wedding-shoot-1.JPG",
      href: "/services/wedding",
      features: [
         "Full day coverage",
         "Engagement session",
         "Online gallery",
         "Print release",
      ],
      price: "From £800",
   },
   {
      title: "Pre-Wedding Shoots",
      description:
         "Beautiful engagement and pre-wedding photography sessions in stunning locations.",
      image: "/images/pre-wedding-shoots/pre-wedding-shoots-1.JPG",
      href: "/services/pre-wedding",
      features: [
         "Location scouting",
         "Outfit changes",
         "Edited photos",
         "Same day preview",
      ],
      price: "From £300",
   },
   {
      title: "Pregnancy Photography",
      description:
         "Celebrate this magical time with elegant maternity portraits that capture the beauty of pregnancy.",
      image: "/images/pregnancy-shoots/pregnancy-shoot-1.jpg",
      href: "/services/pregnancy",
      features: [
         "Studio or outdoor",
         "Partner included",
         "Wardrobe assistance",
         "Digital gallery",
      ],
      price: "From £250",
   },
];

const testimonials = [
   {
      name: "Sarah & James",
      service: "Wedding Photography",
      rating: 5,
      text: "Astral Studios captured our wedding day perfectly! The photos are absolutely stunning and we couldn't be happier with the results. Professional, creative, and so easy to work with.",
      image: "/images/wedding-shoots/wedding-shoot-2.PNG",
   },
   {
      name: "Emma Thompson",
      service: "Pregnancy Photography",
      rating: 5,
      text: "The maternity shoot was such a wonderful experience. The photographer made me feel so comfortable and the photos are beautiful. I'll treasure these memories forever.",
      image: "/images/pregnancy-shoots/pregnancy-shoot-2.JPG",
   },
   {
      name: "Michael & Lisa",
      service: "Pre-Wedding Shoot",
      rating: 5,
      text: "Our engagement photos exceeded all expectations! The creativity and attention to detail was incredible. We can't wait to work with them again for our wedding.",
      image: "/images/pre-wedding-shoots/pre-wedding-shoots-2.JPG",
   },
];

const stats = [
   { icon: Camera, label: "Photos Captured", value: "50,000+" },
   { icon: Users, label: "Happy Clients", value: "500+" },
   { icon: Award, label: "Years Experience", value: "8+" },
   { icon: Video, label: "Events Covered", value: "300+" },
];

export default function Home() {
   return (
      <div className="min-h-screen">
         {/* Hero Section */}
         <HeroSection
            title="Astral Studios"
            subtitle="Professional Photography & Videography"
            description="Capturing life's most precious moments with artistic excellence and creative vision. From weddings to family portraits, we create timeless memories that last forever."
            backgroundImage="/images/hero.JPG"
            primaryCta={{
               text: "View Our Work",
               href: "/portfolio",
            }}
            secondaryCta={{
               text: "Get Quote",
               href: "/contact",
            }}
         />

         {/* Stats Section */}
         <section className="py-16 bg-muted/50">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
               <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
                  {stats.map((stat, index) => (
                     <div key={index} className="text-center">
                        <div className="flex justify-center mb-4">
                           <stat.icon className="h-8 w-8 text-primary" />
                        </div>
                        <div className="text-2xl font-bold text-foreground mb-2">
                           {stat.value}
                        </div>
                        <div className="text-sm text-muted-foreground">
                           {stat.label}
                        </div>
                     </div>
                  ))}
               </div>
            </div>
         </section>

         {/* Featured Services */}
         <section className="py-20">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
               <div className="text-center mb-16">
                  <h2 className="text-3xl md:text-4xl font-bold mb-4">
                     Our Services
                  </h2>
                  <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                     From intimate moments to grand celebrations, we offer
                     comprehensive photography and videography services tailored
                     to your unique needs.
                  </p>
               </div>

               <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                  {featuredServices.map((service, index) => (
                     <ServiceCard key={index} {...service} />
                  ))}
               </div>

               <div className="text-center">
                  <Button asChild size="lg">
                     <Link href="/services">View All Services</Link>
                  </Button>
               </div>
            </div>
         </section>

         {/* Portfolio Preview */}
         <section className="py-20 bg-muted/50">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
               <div className="text-center mb-16">
                  <h2 className="text-3xl md:text-4xl font-bold mb-4">
                     Recent Work
                  </h2>
                  <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                     Take a look at some of our recent photography and
                     videography work that showcases our passion for capturing
                     beautiful moments.
                  </p>
               </div>

               <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
                  <div className="relative aspect-square overflow-hidden rounded-lg group">
                     <Image
                        src="/images/wedding-shoots/wedding-shoot-3.JPG"
                        alt="Wedding photography sample"
                        fill
                        className="object-cover transition-transform duration-300 group-hover:scale-105"
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                     />
                     <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300" />
                     <Badge className="absolute bottom-4 left-4">Wedding</Badge>
                  </div>

                  <div className="relative aspect-square overflow-hidden rounded-lg group">
                     <Image
                        src="/images/pre-wedding-shoots/pre-wedding-shoots-3.JPG"
                        alt="Pre-wedding photography sample"
                        fill
                        className="object-cover transition-transform duration-300 group-hover:scale-105"
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                     />
                     <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300" />
                     <Badge className="absolute bottom-4 left-4">
                        Pre-Wedding
                     </Badge>
                  </div>

                  <div className="relative aspect-square overflow-hidden rounded-lg group">
                     <Image
                        src="/images/pregnancy-shoots/pregnancy-shoot-3.jpg"
                        alt="Pregnancy photography sample"
                        fill
                        className="object-cover transition-transform duration-300 group-hover:scale-105"
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                     />
                     <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300" />
                     <Badge className="absolute bottom-4 left-4">
                        Pregnancy
                     </Badge>
                  </div>
               </div>

               <div className="text-center">
                  <Button asChild variant="outline" size="lg">
                     <Link href="/portfolio">View Full Portfolio</Link>
                  </Button>
               </div>
            </div>
         </section>

         {/* Testimonials */}
         <section className="py-20">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
               <div className="text-center mb-16">
                  <h2 className="text-3xl md:text-4xl font-bold mb-4">
                     What Our Clients Say
                  </h2>
                  <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                     Don't just take our word for it. Here's what our happy
                     clients have to say about their experience with Astral
                     Studios.
                  </p>
               </div>

               <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {testimonials.map((testimonial, index) => (
                     <Card key={index} className="relative">
                        <CardHeader>
                           <div className="flex items-center space-x-4">
                              <div className="relative w-12 h-12 rounded-full overflow-hidden">
                                 <Image
                                    src={testimonial.image}
                                    alt={testimonial.name}
                                    fill
                                    className="object-cover"
                                    sizes="48px"
                                 />
                              </div>
                              <div>
                                 <CardTitle className="text-lg">
                                    {testimonial.name}
                                 </CardTitle>
                                 <CardDescription>
                                    {testimonial.service}
                                 </CardDescription>
                              </div>
                           </div>
                           <div className="flex space-x-1">
                              {[...Array(testimonial.rating)].map((_, i) => (
                                 <Star
                                    key={i}
                                    className="h-4 w-4 fill-primary text-primary"
                                 />
                              ))}
                           </div>
                        </CardHeader>
                        <CardContent>
                           <Quote className="h-6 w-6 text-muted-foreground mb-2" />
                           <p className="text-muted-foreground italic">
                              "{testimonial.text}"
                           </p>
                        </CardContent>
                     </Card>
                  ))}
               </div>
            </div>
         </section>

         {/* CTA Section */}
         <section className="py-20 bg-primary text-primary-foreground">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
               <h2 className="text-3xl md:text-4xl font-bold mb-4">
                  Ready to Capture Your Story?
               </h2>
               <p className="text-lg mb-8 max-w-2xl mx-auto opacity-90">
                  Let's create something beautiful together. Contact us today to
                  discuss your photography and videography needs.
               </p>
               <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" variant="secondary">
                     <Link href="/contact">Get Your Quote</Link>
                  </Button>
                  <Button
                     asChild
                     size="lg"
                     variant="outline"
                     className="border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary"
                  >
                     <Link href="/portfolio">View Our Work</Link>
                  </Button>
               </div>
            </div>
         </section>
      </div>
   );
}
